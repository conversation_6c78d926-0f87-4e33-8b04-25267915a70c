import gi
import random
import time
import threading

gi.require_version("Gtk", "3.0")
from gi.repository import Gtk, Gdk, GdkPixbuf, GLib

# Install with:
# sudo apt-get install python3-gi python3-gi-cairo gir1.2-gtk-3.0


class PetState:
    IDLE = 0
    MOVE = 1
    HIDE = 2


class DesktopPet:
    def __init__(self):
        self.state = PetState.IDLE
        self.window = Gtk.Window(type=Gtk.WindowType.TOPLEVEL)
        self.window.set_title("Desktop Pet")
        self.window.set_decorated(False)
        self.window.set_skip_taskbar_hint(True)
        self.window.set_keep_above(True)
        self.window.set_accept_focus(False)

        # Set transparency
        screen = self.window.get_screen()
        visual = screen.get_rgba_visual()
        if visual and screen.is_composited():
            self.window.set_visual(visual)

        self.window.connect("destroy", Gtk.main_quit)
        self.window.set_app_paintable(True)
        self.window.connect("draw", self.on_draw)

        # Load images for different states
        self.images = {
            PetState.IDLE: GdkPixbuf.Pixbuf.new_from_file("pet_idle.png"),
            PetState.MOVE: GdkPixbuf.Pixbuf.new_from_file("pet_move.png"),
            PetState.HIDE: GdkPixbuf.Pixbuf.new_from_file("pet_hide.png"),
        }

        # Set initial size based on image
        self.window.set_size_request(
            self.images[PetState.IDLE].get_width(),
            self.images[PetState.IDLE].get_height(),
        )

        # Position
        self.x, self.y = 100, 100
        self.window.move(self.x, self.y)

        # Make window draggable
        self.window.connect("button-press-event", self.on_press)
        self.window.connect("button-release-event", self.on_release)
        self.window.connect("motion-notify-event", self.on_motion)
        self.is_dragging = False

        # Start state machine thread
        self.running = True
        self.state_thread = threading.Thread(target=self.state_machine)
        self.state_thread.daemon = True
        self.state_thread.start()

        self.window.show_all()

    def on_draw(self, widget, cr):
        cr.set_source_rgba(0, 0, 0, 0)  # Transparent background
        cr.set_operator(cairo.OPERATOR_SOURCE)
        cr.paint()
        cr.set_operator(cairo.OPERATOR_OVER)

        # Draw current image based on state
        Gdk.cairo_set_source_pixbuf(cr, self.images[self.state], 0, 0)
        cr.paint()
        return False

    def on_press(self, widget, event):
        self.is_dragging = True
        self.drag_x, self.drag_y = event.x, event.y
        return True

    def on_release(self, widget, event):
        self.is_dragging = False
        return True

    def on_motion(self, widget, event):
        if self.is_dragging:
            new_x = int(event.x_root - self.drag_x)
            new_y = int(event.y_root - self.drag_y)
            self.window.move(new_x, new_y)
            self.x, self.y = new_x, new_y
        return True

    def state_machine(self):
        while self.running:
            if self.state == PetState.IDLE:
                # 20% chance to move, 10% chance to hide
                choice = random.random()
                if choice < 0.2:
                    self.state = PetState.MOVE
                elif choice < 0.3:
                    self.state = PetState.HIDE
                time.sleep(3)

            elif self.state == PetState.MOVE:
                # Move in a random direction
                self.x += random.randint(-50, 50)
                self.y += random.randint(-20, 20)

                # Keep on screen
                screen = self.window.get_screen()
                self.x = max(0, min(self.x, screen.get_width() - 100))
                self.y = max(0, min(self.y, screen.get_height() - 100))

                GLib.idle_add(self.window.move, self.x, self.y)
                time.sleep(0.5)
                # 70% chance to go back to idle
                if random.random() < 0.7:
                    self.state = PetState.IDLE

            elif self.state == PetState.HIDE:
                # Stay hidden for 1-3 seconds
                time.sleep(random.uniform(1, 3))
                self.state = PetState.IDLE

            # Update UI with new state
            GLib.idle_add(self.window.queue_draw)


if __name__ == "__main__":
    import cairo  # Import here to avoid early import issues

    pet = DesktopPet()
    Gtk.main()
