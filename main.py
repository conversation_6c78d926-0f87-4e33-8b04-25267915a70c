import gi
import random
import time
import threading

gi.require_version("Gtk", "3.0")
from gi.repository import Gtk, Gdk, GdkPixbuf, GLib

# Install with:
# sudo apt-get install python3-gi python3-gi-cairo gir1.2-gtk-3.0


class PetState:
    IDLE = 0
    MOVE = 1
    HIDE = 2


class DesktopPet:
    def __init__(self):
        print("Initializing Desktop Pet...")
        self.state = PetState.IDLE
        self.window = Gtk.Window(type=Gtk.WindowType.TOPLEVEL)
        self.window.set_title("Desktop Pet")
        self.window.set_decorated(False)
        self.window.set_skip_taskbar_hint(True)
        self.window.set_keep_above(True)
        self.window.set_accept_focus(False)

        # Set transparency
        screen = self.window.get_screen()
        visual = screen.get_rgba_visual()
        if visual and screen.is_composited():
            self.window.set_visual(visual)

        self.window.connect("destroy", Gtk.main_quit)
        self.window.set_app_paintable(True)
        self.window.connect("draw", self.on_draw)

        # Load and scale images for different states (make pet smaller)
        self.scale_factor = 0.5  # Make pet 50% smaller
        print(f"Loading images with scale factor: {self.scale_factor}")

        original_images = {
            PetState.IDLE: GdkPixbuf.Pixbuf.new_from_file("pet_idle.png"),
            PetState.MOVE: GdkPixbuf.Pixbuf.new_from_file("pet_move.png"),
            PetState.HIDE: GdkPixbuf.Pixbuf.new_from_file("pet_hide.png"),
        }

        self.images = {}
        for state, pixbuf in original_images.items():
            original_width = pixbuf.get_width()
            original_height = pixbuf.get_height()
            new_width = int(original_width * self.scale_factor)
            new_height = int(original_height * self.scale_factor)
            print(f"Scaling {state} image from {original_width}x{original_height} to {new_width}x{new_height}")
            self.images[state] = pixbuf.scale_simple(new_width, new_height, GdkPixbuf.InterpType.BILINEAR)

        # Set initial size based on scaled image
        pet_width = self.images[PetState.IDLE].get_width()
        pet_height = self.images[PetState.IDLE].get_height()
        print(f"Setting window size to: {pet_width}x{pet_height}")
        self.window.set_size_request(pet_width, pet_height)

        # Position
        self.x, self.y = 100, 100
        self.window.move(self.x, self.y)

        # Make window draggable
        self.window.connect("button-press-event", self.on_press)
        self.window.connect("button-release-event", self.on_release)
        self.window.connect("motion-notify-event", self.on_motion)
        self.is_dragging = False

        # Start state machine thread
        self.running = True
        self.state_thread = threading.Thread(target=self.state_machine)
        self.state_thread.daemon = True
        self.state_thread.start()

        self.window.show_all()

    def on_draw(self, widget, cr):
        cr.set_source_rgba(0, 0, 0, 0)  # Transparent background
        cr.set_operator(cairo.OPERATOR_SOURCE)
        cr.paint()
        cr.set_operator(cairo.OPERATOR_OVER)

        # Draw current image based on state
        Gdk.cairo_set_source_pixbuf(cr, self.images[self.state], 0, 0)
        cr.paint()
        return False

    def on_press(self, widget, event):
        print("Pet grabbed for dragging")
        self.is_dragging = True
        self.drag_x, self.drag_y = event.x, event.y
        return True

    def on_release(self, widget, event):
        print("Pet released from dragging")
        self.is_dragging = False
        return True

    def on_motion(self, widget, event):
        if self.is_dragging:
            new_x = int(event.x_root - self.drag_x)
            new_y = int(event.y_root - self.drag_y)
            print(f"Dragging pet to position: ({new_x}, {new_y})")
            self.window.move(new_x, new_y)
            self.x, self.y = new_x, new_y
        return True

    def state_machine(self):
        print("Starting pet state machine...")
        while self.running:
            if self.state == PetState.IDLE:
                print("Pet is IDLE, deciding next action...")
                # 20% chance to move, 10% chance to hide
                choice = random.random()
                print(f"Random choice: {choice:.3f}")
                if choice < 0.2:
                    print("Pet decided to MOVE")
                    self.state = PetState.MOVE
                elif choice < 0.3:
                    print("Pet decided to HIDE")
                    self.state = PetState.HIDE
                else:
                    print("Pet staying IDLE")
                time.sleep(3)

            elif self.state == PetState.MOVE:
                print("Pet is MOVING...")
                # Move in a random direction
                old_x, old_y = self.x, self.y
                self.x += random.randint(-50, 50)
                self.y += random.randint(-20, 20)
                print(f"Moving from ({old_x}, {old_y}) to ({self.x}, {self.y})")

                # Keep on screen
                screen = self.window.get_screen()
                screen_width = screen.get_width()
                screen_height = screen.get_height()
                pet_width = self.images[PetState.IDLE].get_width()
                pet_height = self.images[PetState.IDLE].get_height()

                self.x = max(0, min(self.x, screen_width - pet_width))
                self.y = max(0, min(self.y, screen_height - pet_height))
                print(f"Adjusted position to stay on screen: ({self.x}, {self.y})")

                GLib.idle_add(self.window.move, self.x, self.y)
                time.sleep(0.5)
                # 70% chance to go back to idle
                if random.random() < 0.7:
                    print("Pet finished moving, going back to IDLE")
                    self.state = PetState.IDLE
                else:
                    print("Pet continues moving...")

            elif self.state == PetState.HIDE:
                print("Pet is HIDING...")
                # Stay hidden for 1-3 seconds
                hide_duration = random.uniform(1, 3)
                print(f"Hiding for {hide_duration:.1f} seconds")
                time.sleep(hide_duration)
                print("Pet finished hiding, going back to IDLE")
                self.state = PetState.IDLE

            # Update UI with new state
            GLib.idle_add(self.window.queue_draw)


if __name__ == "__main__":
    import cairo  # Import here to avoid early import issues

    pet = DesktopPet()
    Gtk.main()
