import gi
import random
import time
import threading
import subprocess

gi.require_version("Gtk", "3.0")
from gi.repository import Gtk, Gdk, GdkPixbuf, GLib

# Install with:
# sudo apt-get install python3-gi python3-gi-cairo gir1.2-gtk-3.0


class PetState:
    IDLE = 0
    MOVE = 1
    HIDE = 2


class DesktopPet:
    def __init__(self):
        print("Initializing Desktop Pet...")
        self.state = PetState.IDLE
        self.window = Gtk.Window(type=Gtk.WindowType.TOPLEVEL)
        self.window.set_title("Desktop Pet")
        self.window.set_decorated(False)
        self.window.set_skip_taskbar_hint(True)
        self.window.set_keep_above(True)
        self.window.set_accept_focus(False)

        # Set transparency
        screen = self.window.get_screen()
        visual = screen.get_rgba_visual()
        if visual and screen.is_composited():
            self.window.set_visual(visual)

        self.window.connect("destroy", Gtk.main_quit)
        self.window.set_app_paintable(True)
        self.window.connect("draw", self.on_draw)

        # Static pet dimensions
        self.pet_width = 64
        self.pet_height = 64
        print(f"Using static pet size: {self.pet_width}x{self.pet_height}")

        # Load and scale images to static size
        original_images = {
            PetState.IDLE: GdkPixbuf.Pixbuf.new_from_file("pet_idle.png"),
            PetState.MOVE: GdkPixbuf.Pixbuf.new_from_file("pet_move.png"),
            PetState.HIDE: GdkPixbuf.Pixbuf.new_from_file("pet_hide.png"),
        }

        self.images = {}
        for state, pixbuf in original_images.items():
            original_width = pixbuf.get_width()
            original_height = pixbuf.get_height()
            print(f"Scaling {state} image from {original_width}x{original_height} to {self.pet_width}x{self.pet_height}")
            self.images[state] = pixbuf.scale_simple(self.pet_width, self.pet_height, GdkPixbuf.InterpType.BILINEAR)

        # Set window size to static dimensions
        self.window.set_size_request(self.pet_width, self.pet_height)

        # Initialize position with gravity (bottom of screen)
        screen = self.window.get_screen()
        screen_width = screen.get_width()
        screen_height = screen.get_height()

        # Start at random horizontal position, bottom of screen (accounting for taskbar)
        self.x = random.randint(0, screen_width - self.pet_width)
        self.y = screen_height - self.pet_height - 50  # 50px from bottom for taskbar
        print(f"Initial position with gravity: ({self.x}, {self.y})")

        # Movement animation variables
        self.target_x = self.x
        self.is_moving = False
        self.move_speed = 2  # pixels per frame

        # Hide/jump animation variables
        self.is_jumping = False
        self.is_falling = False
        self.jump_velocity = 0
        self.gravity_force = 1

        # Window platform system
        self.current_platform = None  # Current window the pet is standing on
        self.platforms = []  # List of available window platforms

        self.window.move(self.x, self.y)

        # Make window draggable
        self.window.connect("button-press-event", self.on_press)
        self.window.connect("button-release-event", self.on_release)
        self.window.connect("motion-notify-event", self.on_motion)
        self.is_dragging = False

        # Start state machine thread
        self.running = True
        self.state_thread = threading.Thread(target=self.state_machine)
        self.state_thread.daemon = True
        self.state_thread.start()

        self.window.show_all()

    def on_draw(self, widget, cr):
        cr.set_source_rgba(0, 0, 0, 0)  # Transparent background
        cr.set_operator(cairo.OPERATOR_SOURCE)
        cr.paint()
        cr.set_operator(cairo.OPERATOR_OVER)

        # Draw current image based on state
        Gdk.cairo_set_source_pixbuf(cr, self.images[self.state], 0, 0)
        cr.paint()
        return False

    def on_press(self, widget, event):
        print("Pet grabbed for dragging")
        self.is_dragging = True
        self.drag_x, self.drag_y = event.x, event.y
        return True

    def on_release(self, widget, event):
        print("Pet released from dragging - applying gravity")
        self.is_dragging = False
        # Apply gravity when released
        self.apply_gravity()
        return True

    def on_motion(self, widget, event):
        if self.is_dragging:
            new_x = int(event.x_root - self.drag_x)
            new_y = int(event.y_root - self.drag_y)

            # Keep within screen bounds
            screen = self.window.get_screen()
            new_x = max(0, min(new_x, screen.get_width() - self.pet_width))
            new_y = max(0, min(new_y, screen.get_height() - self.pet_height))

            print(f"Dragging pet to position: ({new_x}, {new_y})")
            self.window.move(new_x, new_y)
            self.x, self.y = new_x, new_y
        return True

    def apply_gravity(self):
        """Apply gravity to make pet fall to nearest platform or ground"""
        self.update_platforms()  # Refresh window list

        # Find the platform the pet should land on
        landing_platform = self.find_landing_platform()

        if landing_platform:
            target_y = landing_platform['y'] - self.pet_height  # Stand on top of window
            print(f"Applying gravity: falling to window platform at y={target_y}")
            self.current_platform = landing_platform
        else:
            # Fall to ground (bottom of screen)
            screen = self.window.get_screen()
            target_y = screen.get_height() - self.pet_height - 50  # 50px from bottom for taskbar
            print(f"Applying gravity: falling to ground at y={target_y}")
            self.current_platform = None

        if self.y < target_y:
            # Animate falling
            GLib.timeout_add(16, self.gravity_step, target_y)  # ~60fps

    def gravity_step(self, target_y):
        """Single step of gravity animation"""
        if self.y < target_y:
            self.y += 5  # Fall speed
            if self.y > target_y:
                self.y = target_y
            self.window.move(self.x, self.y)
            return True  # Continue animation
        else:
            platform_info = f" on platform '{self.current_platform['title'][:30]}'" if self.current_platform else " on ground"
            print(f"Pet landed at y={self.y}{platform_info}")
            return False  # Stop animation

    def update_platforms(self):
        """Update the list of available window platforms"""
        self.platforms = self.get_visible_windows()
        print(f"Updated platforms: {len(self.platforms)} windows available")

    def find_landing_platform(self):
        """Find the highest platform below the pet's current position"""
        suitable_platforms = []

        for platform in self.platforms:
            platform_top = platform['y']
            platform_left = platform['x']
            platform_right = platform['x'] + platform['width']

            # Check if platform is below pet and pet's x position overlaps with platform
            if (platform_top > self.y and
                self.x + self.pet_width > platform_left and
                self.x < platform_right):
                suitable_platforms.append(platform)

        if suitable_platforms:
            # Return the highest platform (smallest y value) that the pet can land on
            highest_platform = min(suitable_platforms, key=lambda p: p['y'])
            print(f"Found landing platform: {highest_platform['title'][:30]} at y={highest_platform['y']}")
            return highest_platform

        return None

    def get_visible_windows(self):
        """Get list of visible windows that are not fullscreen"""
        try:
            # Use wmctrl to get window information
            result = subprocess.run(['wmctrl', '-lG'], capture_output=True, text=True)
            if result.returncode != 0:
                print("wmctrl not available, falling back to top of screen")
                return []

            windows = []
            screen = self.window.get_screen()
            screen_width = screen.get_width()
            screen_height = screen.get_height()

            for line in result.stdout.strip().split('\n'):
                if not line:
                    continue

                parts = line.split(None, 7)
                if len(parts) < 8:
                    continue

                # Parse window geometry: window_id desktop x y width height machine title
                try:
                    x, y, width, height = map(int, parts[2:6])
                    title = parts[7] if len(parts) > 7 else ""

                    # Skip our own window and desktop/panel windows
                    if "Desktop Pet" in title or "desktop" in title.lower() or "panel" in title.lower():
                        continue

                    # Skip fullscreen windows (or very large windows)
                    if width >= screen_width * 0.9 and height >= screen_height * 0.9:
                        continue

                    # Skip windows that are off-screen or too small
                    if x < 0 or y < 0 or width < 100 or height < 100:
                        continue

                    windows.append({
                        'x': x,
                        'y': y,
                        'width': width,
                        'height': height,
                        'title': title
                    })

                except ValueError:
                    continue

            print(f"Found {len(windows)} suitable windows")
            return windows

        except Exception as e:
            print(f"Error getting windows: {e}")
            return []

    def state_machine(self):
        print("Starting pet state machine...")
        while self.running:
            if self.state == PetState.IDLE:
                print("Pet is IDLE, deciding next action...")
                # 30% chance to move, 10% chance to hide
                choice = random.random()
                print(f"Random choice: {choice:.3f}")
                if choice < 0.3:
                    print("Pet decided to MOVE")
                    self.start_smooth_movement()
                elif choice < 0.4:
                    print("Pet decided to HIDE")
                    self.state = PetState.HIDE
                else:
                    print("Pet staying IDLE")
                time.sleep(3)

            elif self.state == PetState.MOVE:
                # Movement is handled by smooth animation, just wait
                time.sleep(0.1)
                if not self.is_moving:
                    print("Pet finished moving, going back to IDLE")
                    self.state = PetState.IDLE

            elif self.state == PetState.HIDE:
                if not self.is_jumping and not self.is_falling:
                    print("Pet is HIDING - starting jump sequence...")
                    self.start_jump_hide()
                time.sleep(0.1)

            # Update UI with new state
            GLib.idle_add(self.window.queue_draw)

    def start_smooth_movement(self):
        """Start smooth horizontal movement to a random target"""
        self.update_platforms()  # Refresh window list

        # Determine movement boundaries based on current platform
        if self.current_platform:
            # If on a platform, can only move within platform boundaries
            min_x = self.current_platform['x']
            max_x = self.current_platform['x'] + self.current_platform['width'] - self.pet_width
            print(f"Moving on platform '{self.current_platform['title'][:30]}' from x={min_x} to x={max_x}")
        else:
            # If on ground, can move across entire screen
            screen = self.window.get_screen()
            min_x = 0
            max_x = screen.get_width() - self.pet_width
            print("Moving on ground across entire screen")

        # Choose random target x position within boundaries
        if max_x > min_x:
            self.target_x = random.randint(min_x, max_x)
        else:
            self.target_x = self.x  # Stay in place if platform too small

        print(f"Starting smooth movement from x={self.x} to target_x={self.target_x}")

        self.state = PetState.MOVE
        self.is_moving = True

        # Start smooth movement animation
        GLib.timeout_add(16, self.smooth_move_step)  # ~60fps

    def smooth_move_step(self):
        """Single step of smooth movement animation"""
        if not self.is_moving:
            return False

        # Calculate distance to target
        distance = self.target_x - self.x

        if abs(distance) <= self.move_speed:
            # Close enough, snap to target
            self.x = self.target_x
            self.is_moving = False
            print(f"Pet reached target position: x={self.x}")
            self.window.move(self.x, self.y)

            # Check if pet walked off the platform
            self.check_platform_bounds()
            return False  # Stop animation
        else:
            # Move towards target
            if distance > 0:
                self.x += self.move_speed
            else:
                self.x -= self.move_speed

            self.window.move(self.x, self.y)

            # Check if pet is walking off the platform during movement
            self.check_platform_bounds()
            return True  # Continue animation

    def check_platform_bounds(self):
        """Check if pet has walked off current platform and apply gravity if needed"""
        if self.current_platform:
            platform_left = self.current_platform['x']
            platform_right = self.current_platform['x'] + self.current_platform['width']

            # Check if pet has walked off the platform
            if (self.x + self.pet_width < platform_left or self.x > platform_right):
                print(f"Pet walked off platform '{self.current_platform['title'][:30]}', applying gravity")
                self.current_platform = None
                self.apply_gravity()

    def start_jump_hide(self):
        """Start the jump and hide sequence"""
        print("Pet jumping off screen...")
        self.is_jumping = True
        self.jump_velocity = -15  # Initial upward velocity

        # Start jump animation
        GLib.timeout_add(16, self.jump_step)  # ~60fps

    def jump_step(self):
        """Single step of jump animation"""
        if not self.is_jumping:
            return False

        # Apply physics
        self.y += self.jump_velocity
        self.jump_velocity += self.gravity_force  # Gravity pulls down

        self.window.move(self.x, self.y)

        # Check if pet has jumped off screen (above or below)
        screen = self.window.get_screen()
        if self.y < -self.pet_height or self.y > screen.get_height():
            print("Pet jumped off screen, finding landing spot...")
            self.is_jumping = False
            self.find_landing_spot()
            return False

        return True

    def find_landing_spot(self):
        """Find a window to land on or fall from top of screen"""
        self.update_platforms()  # Refresh window list

        if self.platforms:
            # Choose a random visible window
            target_window = random.choice(self.platforms)
            print(f"Landing on window: {target_window['title'][:50]}...")

            # Position pet fully on top of the window (ensure entire sprite is visible)
            window_left = target_window['x']
            window_right = target_window['x'] + target_window['width']

            # Ensure pet fits entirely on the window
            if target_window['width'] >= self.pet_width:
                # Random position within window bounds, ensuring full sprite is on window
                self.x = random.randint(window_left, window_right - self.pet_width)
            else:
                # Window too narrow, center pet on window
                self.x = window_left + (target_window['width'] - self.pet_width) // 2

            # Position pet on top of window (full sprite above window)
            self.y = target_window['y'] - self.pet_height
            self.current_platform = target_window

            print(f"Pet positioned fully on top of window at ({self.x}, {self.y})")
            print(f"Window bounds: x={window_left} to {window_right}, pet occupies x={self.x} to {self.x + self.pet_width}")
            self.window.move(self.x, self.y)

            # Stay on window for a moment, then return to normal behavior
            GLib.timeout_add(2000, self.finish_hide_sequence)  # 2 seconds
        else:
            print("No suitable windows found, falling from top of screen...")
            self.fall_from_top()

    def finish_hide_sequence(self):
        """Finish the hide sequence and return to normal behavior"""
        print("Pet finished resting on window, returning to IDLE")
        self.state = PetState.IDLE
        return False  # Don't repeat this timeout

    def fall_from_top(self):
        """Make pet fall from top of screen"""
        screen = self.window.get_screen()
        self.x = random.randint(0, screen.get_width() - self.pet_width)
        self.y = -self.pet_height  # Start above screen
        self.current_platform = None  # Not on any platform

        print(f"Pet falling from top at x={self.x}")
        self.is_falling = True
        self.jump_velocity = 0
        GLib.timeout_add(16, self.fall_step)  # ~60fps

    def fall_step(self):
        """Single step of falling animation with platform detection"""
        if not self.is_falling:
            return False

        # Apply gravity
        self.jump_velocity += self.gravity_force
        self.y += self.jump_velocity

        self.window.move(self.x, self.y)

        # Check if pet can land on a platform during fall
        landing_platform = self.find_landing_platform()
        if landing_platform and self.y + self.pet_height >= landing_platform['y']:
            # Land on platform
            self.y = landing_platform['y'] - self.pet_height
            self.current_platform = landing_platform
            self.window.move(self.x, self.y)
            self.is_falling = False
            print(f"Pet landed on platform '{landing_platform['title'][:30]}' at ({self.x}, {self.y})")
            print("Pet finished hiding, going back to IDLE")
            self.state = PetState.IDLE
            return False

        # Check if pet reached the ground (bottom of screen)
        screen = self.window.get_screen()
        ground_y = screen.get_height() - self.pet_height - 50  # 50px from bottom for taskbar

        if self.y >= ground_y:
            self.y = ground_y
            self.current_platform = None  # On ground, not on platform
            self.window.move(self.x, self.y)
            self.is_falling = False
            print(f"Pet landed on ground at ({self.x}, {self.y})")
            print("Pet finished hiding, going back to IDLE")
            self.state = PetState.IDLE
            return False

        return True


if __name__ == "__main__":
    import cairo  # Import here to avoid early import issues

    pet = DesktopPet()
    Gtk.main()
