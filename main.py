import gi
import random
import time
import threading
import subprocess

gi.require_version("Gtk", "3.0")
from gi.repository import Gtk, Gdk, GdkPixbuf, GLib

# Install with:
# sudo apt-get install python3-gi python3-gi-cairo gir1.2-gtk-3.0


class PetState:
    IDLE = 0
    MOVE = 1
    HIDE = 2


class DesktopPet:
    def __init__(self):
        print("Initializing Desktop Pet...")
        self.state = PetState.IDLE
        self.window = Gtk.Window(type=Gtk.WindowType.TOPLEVEL)
        self.window.set_title("Desktop Pet")
        self.window.set_decorated(False)
        self.window.set_skip_taskbar_hint(True)
        self.window.set_keep_above(True)
        self.window.set_accept_focus(False)

        # Set transparency
        screen = self.window.get_screen()
        visual = screen.get_rgba_visual()
        if visual and screen.is_composited():
            self.window.set_visual(visual)

        self.window.connect("destroy", Gtk.main_quit)
        self.window.set_app_paintable(True)
        self.window.connect("draw", self.on_draw)

        # Static pet dimensions
        self.pet_width = 64
        self.pet_height = 64
        print(f"Using static pet size: {self.pet_width}x{self.pet_height}")

        # Load and scale images to static size
        original_images = {
            PetState.IDLE: GdkPixbuf.Pixbuf.new_from_file("pet_idle.png"),
            PetState.MOVE: GdkPixbuf.Pixbuf.new_from_file("pet_move.png"),
            PetState.HIDE: GdkPixbuf.Pixbuf.new_from_file("pet_hide.png"),
        }

        self.images = {}
        for state, pixbuf in original_images.items():
            original_width = pixbuf.get_width()
            original_height = pixbuf.get_height()
            print(f"Scaling {state} image from {original_width}x{original_height} to {self.pet_width}x{self.pet_height}")
            self.images[state] = pixbuf.scale_simple(self.pet_width, self.pet_height, GdkPixbuf.InterpType.BILINEAR)

        # Set window size to static dimensions
        self.window.set_size_request(self.pet_width, self.pet_height)

        # Initialize position with gravity (bottom of screen)
        screen = self.window.get_screen()
        screen_width = screen.get_width()
        screen_height = screen.get_height()

        # Start at random horizontal position, bottom of screen (accounting for taskbar)
        self.x = random.randint(0, screen_width - self.pet_width)
        self.y = screen_height - self.pet_height - 50  # 50px from bottom for taskbar
        print(f"Initial position with gravity: ({self.x}, {self.y})")

        # Movement animation variables
        self.target_x = self.x
        self.is_moving = False
        self.move_speed = 2  # pixels per frame

        # Hide/jump animation variables
        self.is_jumping = False
        self.is_falling = False
        self.jump_velocity = 0
        self.gravity_force = 1

        self.window.move(self.x, self.y)

        # Make window draggable
        self.window.connect("button-press-event", self.on_press)
        self.window.connect("button-release-event", self.on_release)
        self.window.connect("motion-notify-event", self.on_motion)
        self.is_dragging = False

        # Start state machine thread
        self.running = True
        self.state_thread = threading.Thread(target=self.state_machine)
        self.state_thread.daemon = True
        self.state_thread.start()

        self.window.show_all()

    def on_draw(self, widget, cr):
        cr.set_source_rgba(0, 0, 0, 0)  # Transparent background
        cr.set_operator(cairo.OPERATOR_SOURCE)
        cr.paint()
        cr.set_operator(cairo.OPERATOR_OVER)

        # Draw current image based on state
        Gdk.cairo_set_source_pixbuf(cr, self.images[self.state], 0, 0)
        cr.paint()
        return False

    def on_press(self, widget, event):
        print("Pet grabbed for dragging")
        self.is_dragging = True
        self.drag_x, self.drag_y = event.x, event.y
        return True

    def on_release(self, widget, event):
        print("Pet released from dragging - applying gravity")
        self.is_dragging = False
        # Apply gravity when released
        self.apply_gravity()
        return True

    def on_motion(self, widget, event):
        if self.is_dragging:
            new_x = int(event.x_root - self.drag_x)
            new_y = int(event.y_root - self.drag_y)

            # Keep within screen bounds
            screen = self.window.get_screen()
            new_x = max(0, min(new_x, screen.get_width() - self.pet_width))
            new_y = max(0, min(new_y, screen.get_height() - self.pet_height))

            print(f"Dragging pet to position: ({new_x}, {new_y})")
            self.window.move(new_x, new_y)
            self.x, self.y = new_x, new_y
        return True

    def apply_gravity(self):
        """Apply gravity to make pet fall to bottom of screen"""
        screen = self.window.get_screen()
        target_y = screen.get_height() - self.pet_height - 50  # 50px from bottom for taskbar

        if self.y < target_y:
            print(f"Applying gravity: falling from y={self.y} to y={target_y}")
            # Animate falling
            GLib.timeout_add(16, self.gravity_step, target_y)  # ~60fps

    def gravity_step(self, target_y):
        """Single step of gravity animation"""
        if self.y < target_y:
            self.y += 5  # Fall speed
            if self.y > target_y:
                self.y = target_y
            self.window.move(self.x, self.y)
            return True  # Continue animation
        else:
            print(f"Pet landed at y={self.y}")
            return False  # Stop animation

    def get_visible_windows(self):
        """Get list of visible windows that are not fullscreen"""
        try:
            # Use wmctrl to get window information
            result = subprocess.run(['wmctrl', '-lG'], capture_output=True, text=True)
            if result.returncode != 0:
                print("wmctrl not available, falling back to top of screen")
                return []

            windows = []
            screen = self.window.get_screen()
            screen_width = screen.get_width()
            screen_height = screen.get_height()

            for line in result.stdout.strip().split('\n'):
                if not line:
                    continue

                parts = line.split(None, 7)
                if len(parts) < 8:
                    continue

                # Parse window geometry: window_id desktop x y width height machine title
                try:
                    x, y, width, height = map(int, parts[2:6])
                    title = parts[7] if len(parts) > 7 else ""

                    # Skip our own window and desktop/panel windows
                    if "Desktop Pet" in title or "desktop" in title.lower() or "panel" in title.lower():
                        continue

                    # Skip fullscreen windows (or very large windows)
                    if width >= screen_width * 0.9 and height >= screen_height * 0.9:
                        continue

                    # Skip windows that are off-screen or too small
                    if x < 0 or y < 0 or width < 100 or height < 100:
                        continue

                    windows.append({
                        'x': x,
                        'y': y,
                        'width': width,
                        'height': height,
                        'title': title
                    })

                except ValueError:
                    continue

            print(f"Found {len(windows)} suitable windows")
            return windows

        except Exception as e:
            print(f"Error getting windows: {e}")
            return []

    def state_machine(self):
        print("Starting pet state machine...")
        while self.running:
            if self.state == PetState.IDLE:
                print("Pet is IDLE, deciding next action...")
                # 30% chance to move, 10% chance to hide
                choice = random.random()
                print(f"Random choice: {choice:.3f}")
                if choice < 0.3:
                    print("Pet decided to MOVE")
                    self.start_smooth_movement()
                elif choice < 0.4:
                    print("Pet decided to HIDE")
                    self.state = PetState.HIDE
                else:
                    print("Pet staying IDLE")
                time.sleep(3)

            elif self.state == PetState.MOVE:
                # Movement is handled by smooth animation, just wait
                time.sleep(0.1)
                if not self.is_moving:
                    print("Pet finished moving, going back to IDLE")
                    self.state = PetState.IDLE

            elif self.state == PetState.HIDE:
                if not self.is_jumping and not self.is_falling:
                    print("Pet is HIDING - starting jump sequence...")
                    self.start_jump_hide()
                time.sleep(0.1)

            # Update UI with new state
            GLib.idle_add(self.window.queue_draw)

    def start_smooth_movement(self):
        """Start smooth horizontal movement to a random target"""
        screen = self.window.get_screen()
        screen_width = screen.get_width()

        # Choose random target x position (horizontal only)
        self.target_x = random.randint(0, screen_width - self.pet_width)
        print(f"Starting smooth movement from x={self.x} to target_x={self.target_x}")

        self.state = PetState.MOVE
        self.is_moving = True

        # Start smooth movement animation
        GLib.timeout_add(16, self.smooth_move_step)  # ~60fps

    def smooth_move_step(self):
        """Single step of smooth movement animation"""
        if not self.is_moving:
            return False

        # Calculate distance to target
        distance = self.target_x - self.x

        if abs(distance) <= self.move_speed:
            # Close enough, snap to target
            self.x = self.target_x
            self.is_moving = False
            print(f"Pet reached target position: x={self.x}")
            self.window.move(self.x, self.y)
            return False  # Stop animation
        else:
            # Move towards target
            if distance > 0:
                self.x += self.move_speed
            else:
                self.x -= self.move_speed

            self.window.move(self.x, self.y)
            return True  # Continue animation

    def start_jump_hide(self):
        """Start the jump and hide sequence"""
        print("Pet jumping off screen...")
        self.is_jumping = True
        self.jump_velocity = -15  # Initial upward velocity

        # Start jump animation
        GLib.timeout_add(16, self.jump_step)  # ~60fps

    def jump_step(self):
        """Single step of jump animation"""
        if not self.is_jumping:
            return False

        # Apply physics
        self.y += self.jump_velocity
        self.jump_velocity += self.gravity_force  # Gravity pulls down

        self.window.move(self.x, self.y)

        # Check if pet has jumped off screen (above or below)
        screen = self.window.get_screen()
        if self.y < -self.pet_height or self.y > screen.get_height():
            print("Pet jumped off screen, finding landing spot...")
            self.is_jumping = False
            self.find_landing_spot()
            return False

        return True

    def find_landing_spot(self):
        """Find a window to land on or fall from top of screen"""
        windows = self.get_visible_windows()

        if windows:
            # Choose a random visible window
            target_window = random.choice(windows)
            print(f"Landing on window: {target_window['title'][:50]}...")

            # Position pet on top of the window
            self.x = target_window['x'] + random.randint(0, max(0, target_window['width'] - self.pet_width))
            self.y = target_window['y'] - self.pet_height  # On top of window

            print(f"Pet positioned on window at ({self.x}, {self.y})")
            self.window.move(self.x, self.y)

            # Stay on window for a moment, then fall
            GLib.timeout_add(2000, self.start_fall_from_window)  # 2 seconds
        else:
            print("No suitable windows found, falling from top of screen...")
            self.fall_from_top()

    def start_fall_from_window(self):
        """Start falling from current window position"""
        print("Pet falling from window...")
        self.is_falling = True
        self.jump_velocity = 0  # Start with no velocity
        GLib.timeout_add(16, self.fall_step)  # ~60fps
        return False  # Don't repeat this timeout

    def fall_from_top(self):
        """Make pet fall from top of screen"""
        screen = self.window.get_screen()
        self.x = random.randint(0, screen.get_width() - self.pet_width)
        self.y = -self.pet_height  # Start above screen

        print(f"Pet falling from top at x={self.x}")
        self.is_falling = True
        self.jump_velocity = 0
        GLib.timeout_add(16, self.fall_step)  # ~60fps

    def fall_step(self):
        """Single step of falling animation"""
        if not self.is_falling:
            return False

        # Apply gravity
        self.jump_velocity += self.gravity_force
        self.y += self.jump_velocity

        self.window.move(self.x, self.y)

        # Check if pet reached the ground (bottom of screen)
        screen = self.window.get_screen()
        ground_y = screen.get_height() - self.pet_height - 50  # 50px from bottom for taskbar

        if self.y >= ground_y:
            self.y = ground_y
            self.window.move(self.x, self.y)
            self.is_falling = False
            print(f"Pet landed on ground at ({self.x}, {self.y})")
            print("Pet finished hiding, going back to IDLE")
            self.state = PetState.IDLE
            return False

        return True


if __name__ == "__main__":
    import cairo  # Import here to avoid early import issues

    pet = DesktopPet()
    Gtk.main()
